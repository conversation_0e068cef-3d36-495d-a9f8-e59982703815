
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;

    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 0%;

    --muted: 0 0% 80%;
    --muted-foreground: 0 0% 40%;

    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 0%;

    --radius: 0.5rem;

    --navbar-bg: 0 0% 100%;
    --navbar-text: 0 0% 0%;
    --navbar-border: 0 0% 90%;

    --footer-bg: 0 0% 10%;
    --footer-text: 0 0% 100%;
    --footer-text-secondary: 0 0% 60%;

    --card-bg: 0 0% 100%;
    --card-border: 0 0% 90%;

    --input-focus: 0 0% 0%;

    /* Gradient colors for the app */
    --gradient-primary: 220 100% 60%;
    --gradient-secondary: 280 100% 70%;

    /* Score colors */
    --score-excellent: 142 76% 36%;
    --score-good: 43 96% 56%;
    --score-warning: 25 95% 53%;
    --score-error: 0 84% 60%;
    --score-error-bg: 0 93% 94%;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;

    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 60%;

    --accent: 0 0% 10%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 0 0% 100%;

    --navbar-bg: 0 0% 0%;
    --navbar-text: 0 0% 100%;
    --navbar-border: 0 0% 20%;

    --footer-bg: 0 0% 0%;
    --footer-text: 0 0% 100%;
    --footer-text-secondary: 0 0% 60%;

    --card-bg: 0 0% 0%;
    --card-border: 0 0% 20%;

    --input-focus: 0 0% 100%;

    /* Gradient colors for dark mode */
    --gradient-primary: 220 100% 60%;
    --gradient-secondary: 280 100% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
    font-family: 'Chillax', sans-serif;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced animations and transitions */
@layer utilities {
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .glass-effect {
    backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--gradient-primary));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--gradient-secondary));
  }
}
