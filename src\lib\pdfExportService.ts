import html2pdf from 'html2pdf.js';
import { ComprehensiveAnalysisResult } from './uxAnalyzer';

// Interface for PDF export service following Single Responsibility Principle
export interface IPdfExportService {
  exportAnalysisResults(results: ComprehensiveAnalysisResult[]): Promise<void>;
  exportSingleResult(result: ComprehensiveAnalysisResult): Promise<void>;
}

// PDF export configuration interface
export interface PdfExportConfig {
  margin: number;
  filename: string;
  image: { type: string; quality: number };
  html2canvas: { scale: number; useCORS: boolean };
  jsPDF: { unit: string; format: string; orientation: string };
}

// Default PDF configuration
const DEFAULT_PDF_CONFIG: PdfExportConfig = {
  margin: 1,
  filename: 'saascan-analysis-results.pdf',
  image: { type: 'jpeg', quality: 0.98 },
  html2canvas: { scale: 2, useCORS: true },
  jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
};

// PDF export service implementation
export class PdfExportService implements IPdfExportService {
  private config: PdfExportConfig;

  constructor(config: Partial<PdfExportConfig> = {}) {
    this.config = { ...DEFAULT_PDF_CONFIG, ...config };
  }

  async exportAnalysisResults(results: ComprehensiveAnalysisResult[]): Promise<void> {
    const htmlContent = this.generateHtmlContent(results);
    const element = this.createTemporaryElement(htmlContent);
    
    try {
      await this.generatePdf(element, {
        ...this.config,
        filename: `saascan-analysis-${new Date().toISOString().split('T')[0]}.pdf`
      });
    } finally {
      this.removeTemporaryElement(element);
    }
  }

  async exportSingleResult(result: ComprehensiveAnalysisResult): Promise<void> {
    const htmlContent = this.generateHtmlContent([result]);
    const element = this.createTemporaryElement(htmlContent);
    
    try {
      await this.generatePdf(element, {
        ...this.config,
        filename: `saascan-analysis-${result.id}.pdf`
      });
    } finally {
      this.removeTemporaryElement(element);
    }
  }

  private generateHtmlContent(results: ComprehensiveAnalysisResult[]): string {
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>SaasCan Analysis Results</title>
          <style>
            ${this.getPdfStyles()}
          </style>
        </head>
        <body>
          <div class="pdf-container">
            <header class="pdf-header">
              <div class="logo-section">
                <h1>🎯 SaasCan</h1>
                <p>Comprehensive SaaS Idea Analysis</p>
              </div>
              <div class="date-section">
                <p>Generated on ${currentDate}</p>
                <p>Total Analyses: ${results.length}</p>
              </div>
            </header>
            
            <main class="pdf-content">
              ${results.map((result, index) => this.generateResultHtml(result, index + 1)).join('')}
            </main>
            
            <footer class="pdf-footer">
              <p>© 2024 SaasCan - Professional SaaS Analysis Platform</p>
              <p>This report contains confidential business analysis. Handle with care.</p>
            </footer>
          </div>
        </body>
      </html>
    `;
  }

  private generateResultHtml(result: ComprehensiveAnalysisResult, index: number): string {
    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    const getInnovationColor = (level: string) => {
      switch (level) {
        case 'High': return '#10b981';
        case 'Medium': return '#f59e0b';
        case 'Low': return '#ef4444';
        default: return '#6b7280';
      }
    };

    const getRatingColor = (rating: number) => {
      if (rating >= 80) return '#10b981';
      if (rating >= 60) return '#f59e0b';
      return '#ef4444';
    };

    return `
      <div class="analysis-result ${index > 1 ? 'page-break' : ''}">
        <div class="result-header">
          <h2>Analysis #${index}</h2>
          <div class="result-meta">
            <span class="timestamp">📅 ${formatDate(result.timestamp)}</span>
            <span class="rating" style="color: ${getRatingColor(result.overallRating)}">
              🚀 ${result.overallRating}/100
            </span>
          </div>
        </div>

        <div class="analysis-table">
          <table>
            <tbody>
              <tr>
                <td class="label">🎯 Idea</td>
                <td class="value">${result.idea}</td>
              </tr>
              <tr>
                <td class="label">👤 Target Audience</td>
                <td class="value">${result.targetAudience}</td>
              </tr>
              <tr>
                <td class="label">🛠️ Problems Solved</td>
                <td class="value">${result.problemsSolved}</td>
              </tr>
              <tr>
                <td class="label">💡 Proposed Solution</td>
                <td class="value">${result.proposedSolution}</td>
              </tr>
              <tr>
                <td class="label">🔍 Competitors</td>
                <td class="value">${Array.isArray(result.competitors) ? result.competitors.join(', ') : result.competitors}</td>
              </tr>
              <tr>
                <td class="label">📈 Scalability</td>
                <td class="value">${result.scalability}</td>
              </tr>
              <tr>
                <td class="label">💰 Profit Model</td>
                <td class="value">${result.profitModel}</td>
              </tr>
              <tr>
                <td class="label">🧠 Innovation Level</td>
                <td class="value">
                  <span style="color: ${getInnovationColor(result.innovationLevel)}; font-weight: bold;">
                    ${result.innovationLevel}
                  </span>
                </td>
              </tr>
              <tr>
                <td class="label">🚀 Overall Rating</td>
                <td class="value">
                  <span style="color: ${getRatingColor(result.overallRating)}; font-weight: bold; font-size: 1.2em;">
                    ${result.overallRating}/100
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        ${result.summary ? `
          <div class="summary-section">
            <h3>📋 Executive Summary</h3>
            <p>${result.summary}</p>
          </div>
        ` : ''}

        ${result.detailedAnalysis?.recommendations ? `
          <div class="recommendations-section">
            <h3>💡 Key Recommendations</h3>
            <ul>
              ${result.detailedAnalysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
      </div>
    `;
  }

  private getPdfStyles(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #374151;
        background: white;
      }

      .pdf-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 40px;
      }

      .pdf-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 3px solid #3b82f6;
        padding-bottom: 20px;
        margin-bottom: 40px;
      }

      .logo-section h1 {
        font-size: 2.5em;
        color: #3b82f6;
        margin-bottom: 5px;
      }

      .logo-section p {
        color: #6b7280;
        font-size: 1.1em;
      }

      .date-section {
        text-align: right;
        color: #6b7280;
      }

      .analysis-result {
        margin-bottom: 60px;
      }

      .page-break {
        page-break-before: always;
      }

      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e5e7eb;
      }

      .result-header h2 {
        color: #1f2937;
        font-size: 1.8em;
      }

      .result-meta {
        display: flex;
        gap: 20px;
        font-size: 0.9em;
      }

      .analysis-table {
        margin-bottom: 30px;
      }

      .analysis-table table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        overflow: hidden;
      }

      .analysis-table td {
        padding: 15px;
        border-bottom: 1px solid #e5e7eb;
        vertical-align: top;
      }

      .analysis-table .label {
        background-color: #f9fafb;
        font-weight: 600;
        width: 200px;
        color: #374151;
      }

      .analysis-table .value {
        background-color: white;
        color: #1f2937;
      }

      .summary-section, .recommendations-section {
        margin-top: 30px;
        padding: 20px;
        background-color: #f8fafc;
        border-radius: 8px;
        border-left: 4px solid #3b82f6;
      }

      .summary-section h3, .recommendations-section h3 {
        color: #1f2937;
        margin-bottom: 15px;
        font-size: 1.3em;
      }

      .recommendations-section ul {
        list-style: none;
        padding-left: 0;
      }

      .recommendations-section li {
        padding: 8px 0;
        padding-left: 20px;
        position: relative;
      }

      .recommendations-section li:before {
        content: "→";
        position: absolute;
        left: 0;
        color: #3b82f6;
        font-weight: bold;
      }

      .pdf-footer {
        margin-top: 60px;
        padding-top: 20px;
        border-top: 2px solid #e5e7eb;
        text-align: center;
        color: #6b7280;
        font-size: 0.9em;
      }

      .pdf-footer p {
        margin-bottom: 5px;
      }
    `;
  }

  private createTemporaryElement(htmlContent: string): HTMLElement {
    const element = document.createElement('div');
    element.innerHTML = htmlContent;
    element.style.position = 'absolute';
    element.style.left = '-9999px';
    element.style.top = '-9999px';
    document.body.appendChild(element);
    return element;
  }

  private removeTemporaryElement(element: HTMLElement): void {
    if (element && element.parentNode) {
      element.parentNode.removeChild(element);
    }
  }

  private async generatePdf(element: HTMLElement, config: PdfExportConfig): Promise<void> {
    return html2pdf()
      .set(config)
      .from(element)
      .save();
  }
}

// Factory function for creating PDF export service (following Factory Pattern)
export const createPdfExportService = (config?: Partial<PdfExportConfig>): IPdfExportService => {
  return new PdfExportService(config);
};
