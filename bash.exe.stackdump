Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFFBF30000 ntdll.dll
7FFFFA950000 KERNEL32.DLL
7FFFF92B0000 KERNELBASE.dll
7FFFFBA20000 USER32.dll
7FFFF9C60000 win32u.dll
7FFFFBD10000 GDI32.dll
7FFFF98F0000 gdi32full.dll
000210040000 msys-2.0.dll
7FFFF9850000 msvcp_win.dll
7FFFF9A10000 ucrtbase.dll
7FFFFBC60000 advapi32.dll
7FFFFA160000 msvcrt.dll
7FFFFB0B0000 sechost.dll
7FFFFAF90000 RPCRT4.dll
7FFFF8990000 CRYPTBASE.DLL
7FFFF9660000 bcryptPrimitives.dll
7FFFFA540000 IMM32.DLL
