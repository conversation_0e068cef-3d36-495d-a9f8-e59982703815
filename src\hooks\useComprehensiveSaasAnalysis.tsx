import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { ComprehensiveAnalysisResult, ComprehensiveAnalysisService, IAnalysisService } from "@/lib/uxAnalyzer";
import { COMPREHENSIVE_SAAS_TABLE_ANALYSIS_PROMPT } from "@/config/analysisPrompts";
import {
  saveToLocalStorage,
  loadFromLocalStorage,
  loadUserPreferences,
  saveUserPreferences,
} from "@/lib/storage";
import {
  measureAnalysisTime,
  trackUserAction,
  trackError,
} from "@/lib/performance";
import { getGeminiApiKey, getApiConfig } from "@/lib/config";

// Custom hook for comprehensive SaaS analysis following SOLID principles
const useComprehensiveSaasAnalysis = (language: string) => {
  const [input, setInput] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [results, setResults] = useState<ComprehensiveAnalysisResult[]>([]);
  const [preferences, setPreferences] = useState(loadUserPreferences());
  const { toast } = useToast();

  // Dependency injection - analysis service
  const [analysisService] = useState<IAnalysisService>(() => new ComprehensiveAnalysisService());

  // Load data on mount with enhanced error handling
  useEffect(() => {
    try {
      const savedResults = loadFromLocalStorage();
      // Filter and convert legacy results if needed
      const comprehensiveResults = savedResults
        .filter(result => 'targetAudience' in result)
        .map(result => result as ComprehensiveAnalysisResult);
      
      if (comprehensiveResults.length > 0) {
        setResults(comprehensiveResults);
      }

      // Load user preferences
      const userPrefs = loadUserPreferences();
      setPreferences(userPrefs);

      if (userPrefs.autoSave) {
        console.log("Auto-save enabled for comprehensive analysis");
      }
    } catch (error) {
      console.error("Failed to load initial data:", error);
      toast({
        title: "Loading Error",
        description: "Some data couldn't be loaded. Starting fresh.",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Enhanced save function with preferences
  const saveResults = useCallback(
    (newResults: ComprehensiveAnalysisResult[]) => {
      try {
        const success = saveToLocalStorage(newResults);
        if (!success) {
          throw new Error("Failed to save to localStorage");
        }
        setResults(newResults);

        if (preferences.notifications) {
          toast({
            title: "Data Saved",
            description: "Your analysis has been saved successfully",
          });
        }
      } catch (error) {
        console.error("Failed to save results:", error);
        toast({
          title: "Save Error",
          description: "Failed to save your analysis. Please try again.",
          variant: "destructive",
        });
      }
    },
    [preferences.notifications, toast]
  );

  const handleAnalyze = async () => {
    if (!input.trim()) {
      trackUserAction("comprehensive_analysis_attempted_empty_input");
      toast({
        title: "Input Required",
        description: "Please enter your SaaS idea to analyze",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    const endTiming = measureAnalysisTime();
    trackUserAction("comprehensive_analysis_started", {
      inputLength: input.length.toString(),
    });

    try {
      const apiConfig = getApiConfig();
      let GEMINI_API_KEY: string;

      try {
        GEMINI_API_KEY = getGeminiApiKey();
      } catch (error) {
        console.log("No API key found, using mock comprehensive analysis");
        const mockResult = await analysisService.analyzeComprehensive(input, "en");

        const updatedResults = [mockResult, ...results];
        saveResults(updatedResults);
        setInput("");
        endTiming();
        trackUserAction("comprehensive_analysis_completed_mock");

        toast({
          title: "Analysis Complete",
          description: "Your SaaS idea has been analyzed successfully",
        });
        return;
      }

      // Insert the input into our comprehensive analysis prompt
      const prompt = COMPREHENSIVE_SAAS_TABLE_ANALYSIS_PROMPT.replace(
        "{SAAS_CONCEPT}",
        input
      );

      const response = await fetch(
        `${apiConfig.gemini.baseUrl}/${apiConfig.gemini.model}:generateContent?key=${GEMINI_API_KEY}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: prompt,
                  },
                ],
              },
            ],
            generationConfig: {
              temperature: apiConfig.gemini.temperature,
              topK: apiConfig.gemini.topK,
              topP: apiConfig.gemini.topP,
              maxOutputTokens: apiConfig.gemini.maxTokens,
            },
            safetySettings: [
              {
                category: "HARM_CATEGORY_HARASSMENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
              {
                category: "HARM_CATEGORY_HATE_SPEECH",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
              {
                category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
              {
                category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error?.message || `Gemini API Error: ${response.status}`
        );
      }

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!content) {
        throw new Error("No response content received from Gemini API");
      }

      let parsed: Partial<ComprehensiveAnalysisResult> = {};
      try {
        const cleanContent = content.replace(/```json\n?|\n?```/g, "").trim();
        parsed = JSON.parse(cleanContent);
      } catch (parseError) {
        console.error("Failed to parse Gemini response:", content);
        // Fallback to mock analysis if parsing fails
        const mockResult = await analysisService.analyzeComprehensive(input, "en");
        parsed = mockResult;
      }

      const newResult: ComprehensiveAnalysisResult = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        language: "en",
        idea: input,
        targetAudience: parsed.targetAudience || "Small to medium businesses",
        problemsSolved: parsed.problemsSolved || "Efficiency and productivity challenges",
        proposedSolution: parsed.proposedSolution || "Automated workflow solution",
        competitors: Array.isArray(parsed.competitors) ? parsed.competitors : ["Generic competitors"],
        scalability: parsed.scalability || "Moderate scalability potential",
        profitModel: parsed.profitModel || "Subscription-based model",
        innovationLevel: parsed.innovationLevel || "Medium",
        overallRating: typeof parsed.overallRating === "number" 
          ? Math.max(45, Math.min(95, parsed.overallRating))
          : 70,
        summary: parsed.summary || "Moderate potential SaaS concept requiring further validation",
        detailedAnalysis: parsed.detailedAnalysis || {
          marketAnalysis: "Market analysis pending",
          technicalFeasibility: "Technical assessment needed",
          competitiveAdvantage: "Competitive positioning to be determined",
          riskAssessment: "Risk evaluation required",
          recommendations: [
            "Conduct market research",
            "Validate problem-solution fit",
            "Develop MVP",
            "Test with target users"
          ]
        }
      };

      const updatedResults = [newResult, ...results];
      saveResults(updatedResults);
      setInput("");
      endTiming();
      trackUserAction("comprehensive_analysis_completed_api", {
        rating: newResult.overallRating.toString(),
        innovationLevel: newResult.innovationLevel,
      });

      toast({
        title: "Analysis Complete",
        description: "Your SaaS idea has been analyzed with comprehensive insights",
      });
    } catch (error) {
      console.error("Comprehensive analysis error:", error);
      endTiming();
      trackError("comprehensive_analysis_failed", {
        errorType: error instanceof Error ? error.name : "unknown",
        errorMessage: error instanceof Error ? error.message : "unknown",
      });

      toast({
        title: "Analysis Error",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleClear = useCallback(() => {
    try {
      setResults([]);
      saveToLocalStorage([]);
      toast({
        title: "History Cleared",
        description: "All analysis results have been cleared",
      });
    } catch (error) {
      console.error("Failed to clear results:", error);
      toast({
        title: "Clear Error",
        description: "Failed to clear analysis history",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Update user preferences
  const updatePreferences = useCallback(
    (newPrefs: Partial<typeof preferences>) => {
      try {
        const updatedPrefs = { ...preferences, ...newPrefs };
        setPreferences(updatedPrefs);
        saveUserPreferences(updatedPrefs);

        if (preferences.notifications) {
          toast({
            title: "Preferences Updated",
            description: "Your settings have been saved",
          });
        }
      } catch (error) {
        console.error("Failed to update preferences:", error);
        toast({
          title: "Settings Error",
          description: "Failed to save your preferences",
          variant: "destructive",
        });
      }
    },
    [preferences, toast]
  );

  return {
    input,
    setInput,
    isAnalyzing,
    results,
    preferences,
    handleAnalyze,
    handleClear,
    updatePreferences,
    saveResults,
  };
};

export default useComprehensiveSaasAnalysis;
