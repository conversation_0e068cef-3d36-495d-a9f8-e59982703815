import { useState, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { HorizontalAnalysisResult } from "@/lib/uxAnalyzer";
import { analysisStorage } from "@/lib/analysisStorage";
import { ARABIC_HORIZONTAL_TABLE_ANALYSIS_PROMPT } from "@/config/analysisPrompts";
import {
  measureAnalysisTime,
  trackUserAction,
  trackError,
} from "@/lib/performance";
import { getGeminiApiKey, getApiConfig } from "@/lib/config";

// Mock data for fallback analysis
const generateMockAnalysis = (originalIdea: string): HorizontalAnalysisResult => {
  const mockTargetAudiences = [
    "الشركات الصغيرة والمتوسطة في قطاع التجارة الإلكترونية",
    "فرق تطوير البرمجيات في الشركات الكبيرة",
    "وكالات التسويق الرقمي والاستشاريين",
    "مقدمي الرعاية الصحية والعيادات الطبية",
    "المؤسسات التعليمية ومنصات التعلم الإلكتروني"
  ];

  const mockProblems = [
    "عدم كفاءة العمليات اليدوية مما يؤدي إلى ضياع الوقت والأخطاء",
    "نقص في رؤية البيانات الفورية والتحليلات",
    "ضعف التواصل والتعاون بين الفرق",
    "صعوبة في تتبع وإدارة علاقات العملاء",
    "تحديات في إدارة سير العمل وتنسيق المهام"
  ];

  const mockSolutions = [
    "منصة أتمتة قائمة على السحابة مع تحسين سير العمل بالذكاء الاصطناعي",
    "لوحة تحكم فورية مع تحليلات متقدمة ورؤى تنبؤية",
    "مركز تواصل متكامل مع إمكانيات إدارة المشاريع",
    "نظام إدارة علاقات العملاء الشامل مع رعاية العملاء المحتملين الآلية",
    "إدارة المهام الذكية مع تحسين تخصيص الموارد"
  ];

  const mockCompetitors = [
    ["سيلزفورس", "هابسبوت", "بايبدرايف"],
    ["مايكروسوفت تيمز", "سلاك", "أسانا"],
    ["تابلو", "باور بي آي", "لوكر"],
    ["أمازون ويب سيرفيسز", "مايكروسوفت أزور", "جوجل كلاود"],
    ["شوبيفاي", "ووكومرس", "بيج كومرس"]
  ];

  const mockScalability = [
    "التوسع الأفقي مع بنية الخدمات المصغرة والحاويات",
    "التوسع العالمي من خلال النشر السحابي متعدد المناطق",
    "توسع السوق العمودي مع وحدات خاصة بالصناعة",
    "نهج API أولاً يمكن التكامل مع الأطراف الثالثة والشراكات",
    "حلول العلامة البيضاء لقنوات البائعين والشركاء"
  ];

  const mockRevenueModels = [
    "نموذج فريميوم مع ميزات متميزة وتحليلات متقدمة",
    "تسعير الاشتراك المتدرج بناءً على الاستخدام والميزات",
    "تسعير لكل مقعد مع خصومات حجم للمؤسسات",
    "تسعير قائم على الاستخدام مع نموذج الدفع حسب التوسع",
    "ترخيص المؤسسات مع خدمات التنفيذ المخصصة"
  ];

  const innovationLevels: ("منخفض" | "متوسط" | "عالي")[] = ["منخفض", "متوسط", "عالي"];
  
  // Simple scoring based on input length and keywords
  const inputLower = originalIdea.toLowerCase();
  let baseScore = 65;
  
  if (inputLower.includes("ai") || inputLower.includes("ذكاء")) baseScore += 10;
  if (inputLower.includes("automation") || inputLower.includes("أتمتة")) baseScore += 8;
  if (inputLower.includes("cloud") || inputLower.includes("سحابة")) baseScore += 5;
  if (originalIdea.length > 100) baseScore += 5;
  
  const finalScore = Math.max(45, Math.min(95, baseScore + Math.floor(Math.random() * 10) - 5));
  
  return {
    id: `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    timestamp: new Date().toISOString(),
    language: "ar",
    originalIdea,
    targetAudience: mockTargetAudiences[Math.floor(Math.random() * mockTargetAudiences.length)],
    problemsSolved: mockProblems[Math.floor(Math.random() * mockProblems.length)],
    proposedSolution: mockSolutions[Math.floor(Math.random() * mockSolutions.length)],
    competitors: mockCompetitors[Math.floor(Math.random() * mockCompetitors.length)],
    scalability: mockScalability[Math.floor(Math.random() * mockScalability.length)],
    revenueModel: mockRevenueModels[Math.floor(Math.random() * mockRevenueModels.length)],
    innovationLevel: innovationLevels[Math.floor(Math.random() * innovationLevels.length)],
    overallScore: finalScore
  };
};

// Custom hook for single analysis mode
const useSingleAnalysis = () => {
  const [input, setInput] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentResult, setCurrentResult] = useState<HorizontalAnalysisResult | null>(null);
  const { toast } = useToast();

  const handleAnalyze = useCallback(async () => {
    if (!input.trim()) {
      trackUserAction("single_analysis_attempted_empty_input");
      toast({
        title: "مطلوب إدخال النص",
        description: "يرجى إدخال فكرة البرمجيات كخدمة للتحليل",
        variant: "destructive",
      });
      return;
    }

    // Clear previous result
    setCurrentResult(null);
    setIsAnalyzing(true);
    
    const endTiming = measureAnalysisTime();
    trackUserAction("single_analysis_started", {
      inputLength: input.length.toString(),
    });

    try {
      const apiConfig = getApiConfig();
      let GEMINI_API_KEY: string;

      try {
        GEMINI_API_KEY = getGeminiApiKey();
      } catch (error) {
        console.log("No API key found, using mock analysis");
        const mockResult = generateMockAnalysis(input);
        
        // Save to history
        analysisStorage.saveAnalysis(mockResult);
        
        setCurrentResult(mockResult);
        setInput("");
        endTiming();
        trackUserAction("single_analysis_completed_mock");

        toast({
          title: "تم التحليل بنجاح",
          description: "تم تحليل فكرة البرمجيات كخدمة بنجاح",
        });
        return;
      }

      // Use the Arabic horizontal table analysis prompt
      const prompt = ARABIC_HORIZONTAL_TABLE_ANALYSIS_PROMPT.replace(
        "{SAAS_CONCEPT}",
        input
      );

      const response = await fetch(
        `${apiConfig.gemini.baseUrl}/${apiConfig.gemini.model}:generateContent?key=${GEMINI_API_KEY}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            contents: [
              {
                parts: [
                  {
                    text: prompt,
                  },
                ],
              },
            ],
            generationConfig: {
              temperature: apiConfig.gemini.temperature,
              topK: apiConfig.gemini.topK,
              topP: apiConfig.gemini.topP,
              maxOutputTokens: apiConfig.gemini.maxTokens,
            },
            safetySettings: [
              {
                category: "HARM_CATEGORY_HARASSMENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
              {
                category: "HARM_CATEGORY_HATE_SPEECH",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
              {
                category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
              {
                category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                threshold: "BLOCK_MEDIUM_AND_ABOVE",
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error?.message || `Gemini API Error: ${response.status}`
        );
      }

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!content) {
        throw new Error("No response content received from Gemini API");
      }

      let parsed: Partial<HorizontalAnalysisResult> = {};
      try {
        const cleanContent = content.replace(/```json\n?|\n?```/g, "").trim();
        parsed = JSON.parse(cleanContent);
      } catch (parseError) {
        console.error("Failed to parse Gemini response:", content);
        // Fallback to mock analysis if parsing fails
        const mockResult = generateMockAnalysis(input);
        setCurrentResult(mockResult);
        analysisStorage.saveAnalysis(mockResult);
        setInput("");
        endTiming();
        trackUserAction("single_analysis_completed_fallback");
        
        toast({
          title: "تم التحليل بنجاح",
          description: "تم تحليل فكرة البرمجيات كخدمة بنجاح (وضع احتياطي)",
        });
        return;
      }

      const newResult: HorizontalAnalysisResult = {
        id: `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date().toISOString(),
        language: "ar",
        originalIdea: input,
        targetAudience: parsed.targetAudience || "الشركات الصغيرة والمتوسطة",
        problemsSolved: parsed.problemsSolved || "تحديات الكفاءة والإنتاجية",
        proposedSolution: parsed.proposedSolution || "حل سير العمل الآلي",
        competitors: Array.isArray(parsed.competitors) ? parsed.competitors : ["منافسون عامون"],
        scalability: parsed.scalability || "إمكانية توسع متوسطة",
        revenueModel: parsed.revenueModel || "نموذج قائم على الاشتراك",
        innovationLevel: parsed.innovationLevel || "متوسط",
        overallScore: typeof parsed.overallScore === "number" 
          ? Math.max(45, Math.min(95, parsed.overallScore))
          : 70
      };

      // Save to history
      analysisStorage.saveAnalysis(newResult);
      
      setCurrentResult(newResult);
      setInput("");
      endTiming();
      trackUserAction("single_analysis_completed_api", {
        score: newResult.overallScore.toString(),
        innovationLevel: newResult.innovationLevel,
      });

      toast({
        title: "تم التحليل بنجاح",
        description: "تم تحليل فكرة البرمجيات كخدمة مع رؤى شاملة",
      });
    } catch (error) {
      console.error("Single analysis error:", error);
      endTiming();
      trackError("single_analysis_failed", {
        errorType: error instanceof Error ? error.name : "unknown",
        errorMessage: error instanceof Error ? error.message : "unknown",
      });

      toast({
        title: "خطأ في التحليل",
        description:
          error instanceof Error
            ? error.message
            : "حدث خطأ غير متوقع",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  }, [input, toast]);

  const clearResult = useCallback(() => {
    setCurrentResult(null);
  }, []);

  return {
    input,
    setInput,
    isAnalyzing,
    currentResult,
    handleAnalyze,
    clearResult,
  };
};

export default useSingleAnalysis;
