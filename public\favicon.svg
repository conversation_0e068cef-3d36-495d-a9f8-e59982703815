<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="32" height="32"><svg width="32" height="32" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer Circle -->
  <circle cx="60" cy="60" r="35" stroke="currentColor" stroke-width="8" fill="none"></circle>
  <!-- Bar 1 -->
  <rect x="48" y="75" width="8" height="18" fill="none" stroke="currentColor" stroke-width="3" rx="2" ry="2"></rect>
  <!-- Bar 2 -->
  <rect x="60" y="60" width="8" height="33" fill="none" stroke="currentColor" stroke-width="3" rx="2" ry="2"></rect>
  <!-- Bar 3 -->
  <rect x="72" y="45" width="8" height="48" fill="none" stroke="currentColor" stroke-width="3" rx="2" ry="2"></rect>
  <!-- Rotated Rectangle -->
  <rect x="87" y="87" width="16" height="36" rx="8" ry="8" fill="currentColor" transform="rotate(-45 87 87)"></rect>
</svg><style>@media (prefers-color-scheme: light) { :root { filter: contrast(1) brightness(1); } }
@media (prefers-color-scheme: dark) { :root { filter: contrast(0.3846153846153846) brightness(4.5); } }
</style></svg>